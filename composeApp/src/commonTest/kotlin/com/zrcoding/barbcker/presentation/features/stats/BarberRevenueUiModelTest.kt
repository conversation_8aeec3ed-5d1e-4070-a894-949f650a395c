package com.zrcoding.barbcker.presentation.features.stats

import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.models.BarberRevenue
import com.zrcoding.barbcker.presentation.features.common.model.toUiModel
import kotlin.test.Test
import kotlin.test.assertEquals

class BarberRevenueUiModelTest {

    @Test
    fun `toUiModel should format revenue and tips with currency symbol`() {
        val barber = <PERSON>(
            uuid = "test-uuid",
            name = "<PERSON>",
            commissionRate = 70,
            phoneNumber = "+1234567890"
        )

        val barberRevenue = BarberRevenue(
            barber = barber,
            totalHaircuts = 15,
            totalRevenue = 450.75,
            totalTips = 75.25
        )

        val uiModel = barberRevenue.toUiModel(currencySymbol = "$")

        assertEquals("test-uuid", uiModel.barberId)
        assertEquals("<PERSON>", uiModel.barberName)
        assertEquals(15, uiModel.totalHaircuts)
        assertEquals("450.75 $", uiModel.totalRevenue) // Rounded to 2 decimal places
        assertEquals("75.25 $", uiModel.totalTips)
    }

    @Test
    fun `toUiModel should handle different currency symbols`() {
        val barber = Barber(
            uuid = "test-uuid",
            name = "Jane Smith",
            commissionRate = 65,
            phoneNumber = "+1234567891"
        )

        val barberRevenue = BarberRevenue(
            barber = barber,
            totalHaircuts = 10,
            totalRevenue = 300.0,
            totalTips = 50.0
        )

        // Test with Euro symbol
        val euroUiModel = barberRevenue.toUiModel(currencySymbol = "€")
        assertEquals("300.0 €", euroUiModel.totalRevenue)
        assertEquals("50.0 €", euroUiModel.totalTips)

        // Test with Pound symbol
        val poundUiModel = barberRevenue.toUiModel(currencySymbol = "£")
        assertEquals("300.0 £", poundUiModel.totalRevenue)
        assertEquals("50.0 £", poundUiModel.totalTips)
    }

    @Test
    fun `toUiModel should handle zero values correctly`() {
        val barber = Barber(
            uuid = "test-uuid",
            name = "New Barber",
            commissionRate = 60,
            phoneNumber = "+1234567892"
        )

        val barberRevenue = BarberRevenue(
            barber = barber,
            totalHaircuts = 0,
            totalRevenue = 0.0,
            totalTips = 0.0
        )

        val uiModel = barberRevenue.toUiModel(currencySymbol = "$")

        assertEquals(0, uiModel.totalHaircuts)
        assertEquals("0.0 $", uiModel.totalRevenue)
        assertEquals("0.0 $", uiModel.totalTips)
    }

    @Test
    fun `toUiModel should handle large numbers correctly`() {
        val barber = Barber(
            uuid = "test-uuid",
            name = "Top Barber",
            commissionRate = 80,
            phoneNumber = "+1234567893"
        )

        val barberRevenue = BarberRevenue(
            barber = barber,
            totalHaircuts = 1000,
            totalRevenue = 25000.99,
            totalTips = 5000.50
        )

        val uiModel = barberRevenue.toUiModel(currencySymbol = "$")

        assertEquals(1000, uiModel.totalHaircuts)
        assertEquals("25000.99 $", uiModel.totalRevenue) // Rounded to 2 decimal places
        assertEquals("5000.5 $", uiModel.totalTips)
    }

    @Test
    fun `toUiModel should handle decimal rounding correctly`() {
        val barber = Barber(
            uuid = "test-uuid",
            name = "Test Barber",
            commissionRate = 70,
            phoneNumber = "+1234567894"
        )

        val testCases = listOf(
            // (revenue, expected rounded revenue with 2 decimal places)
            100.4 to "100.4",
            100.5 to "100.5",
            100.6 to "100.6",
            99.1 to "99.1",
            99.9 to "99.9"
        )

        testCases.forEach { (revenue, expectedRevenue) ->
            val barberRevenue = BarberRevenue(
                barber = barber,
                totalHaircuts = 5,
                totalRevenue = revenue,
                totalTips = 10.0
            )

            val uiModel = barberRevenue.toUiModel(currencySymbol = "$")
            assertEquals("$expectedRevenue $", uiModel.totalRevenue)
        }
    }

    @Test
    fun `toUiModel should preserve barber information correctly`() {
        val barber = Barber(
            uuid = "unique-barber-id",
            name = "Professional Barber",
            commissionRate = 75,
            phoneNumber = "+1987654321"
        )

        val barberRevenue = BarberRevenue(
            barber = barber,
            totalHaircuts = 20,
            totalRevenue = 600.0,
            totalTips = 120.0
        )

        val uiModel = barberRevenue.toUiModel(currencySymbol = "€")

        assertEquals("unique-barber-id", uiModel.barberId)
        assertEquals("Professional Barber", uiModel.barberName)
        assertEquals(20, uiModel.totalHaircuts)
        // Commission rate and phone number are not included in UI model
        // but barber info should be preserved correctly
    }

    @Test
    fun `toUiModel should handle empty currency symbol`() {
        val barber = Barber(
            uuid = "test-uuid",
            name = "Test Barber",
            commissionRate = 70,
            phoneNumber = "+1234567890"
        )

        val barberRevenue = BarberRevenue(
            barber = barber,
            totalHaircuts = 5,
            totalRevenue = 150.0,
            totalTips = 30.0
        )

        val uiModel = barberRevenue.toUiModel(currencySymbol = "")

        assertEquals("150.0 ", uiModel.totalRevenue)
        assertEquals("30.0 ", uiModel.totalTips)
    }
}
