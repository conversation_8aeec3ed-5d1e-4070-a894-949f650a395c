package com.zrcoding.barbcker.presentation.features.stats.mocks

import app.cash.paging.PagingData
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.models.BarberRevenue
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

class MockStatsRepository : StatsRepository {

    private var totalRevenue: Double = 1000.0
    private var shopOwnerTotalRevenue: Double = 300.0
    private var totalTips: Double = 200.0
    private var barbersRevenue: List<BarberRevenue> = emptyList()

    // Test data setup methods
    fun setTotalRevenue(revenue: Double) {
        totalRevenue = revenue
    }

    fun setShopOwnerTotalRevenue(revenue: Double) {
        shopOwnerTotalRevenue = revenue
    }

    fun setTotalTips(tips: Double) {
        totalTips = tips
    }

    fun setBarbersRevenue(revenue: List<BarberRevenue>) {
        barbersRevenue = revenue
    }

    override fun getTotalRevenueForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double> {
        return flowOf(totalRevenue)
    }

    override fun getShopOwnerTotalRevenueForPeriod(
        startTimestamp: Long,
        endTimestamp: Long
    ): Flow<Double> {
        return flowOf(shopOwnerTotalRevenue)
    }

    override fun getTotalTipsForPeriod(startTimestamp: Long, endTimestamp: Long): Flow<Double> {
        return flowOf(totalTips)
    }

    override fun getBarbersRevenueForPeriod(
        startTimestamp: Long,
        endTimestamp: Long
    ): Flow<PagingData<BarberRevenue>> {
        return flowOf(PagingData.from(barbersRevenue))
    }

    companion object {
        fun createSampleBarbersRevenue(): List<BarberRevenue> {
            return listOf(
                BarberRevenue(
                    barber = Barber(
                        uuid = "1",
                        name = "John Doe",
                        commissionRate = 70,
                        phoneNumber = "+1234567890"
                    ),
                    totalHaircuts = 15,
                    totalRevenue = 450.0,
                    totalTips = 75.0
                ),
                BarberRevenue(
                    barber = Barber(
                        uuid = "2",
                        name = "Jane Smith",
                        commissionRate = 65,
                        phoneNumber = "+1234567891"
                    ),
                    totalHaircuts = 12,
                    totalRevenue = 360.0,
                    totalTips = 60.0
                ),
                BarberRevenue(
                    barber = Barber(
                        uuid = "3",
                        name = "Mike Johnson",
                        commissionRate = 75,
                        phoneNumber = "+1234567892"
                    ),
                    totalHaircuts = 8,
                    totalRevenue = 240.0,
                    totalTips = 40.0
                )
            )
        }
    }
}
