package com.zrcoding.barbcker.presentation.features.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.ic_currency
import barbcker.composeapp.generated.resources.ic_info
import barbcker.composeapp.generated.resources.ic_language
import barbcker.composeapp.generated.resources.ic_lock
import barbcker.composeapp.generated.resources.ic_logout
import barbcker.composeapp.generated.resources.ic_whatsapp
import barbcker.composeapp.generated.resources.img_barber1
import barbcker.composeapp.generated.resources.settings_about_us
import barbcker.composeapp.generated.resources.settings_choose_language
import barbcker.composeapp.generated.resources.settings_contact_us
import barbcker.composeapp.generated.resources.settings_currency
import barbcker.composeapp.generated.resources.settings_delete_account
import barbcker.composeapp.generated.resources.settings_delete_account_confirmation
import barbcker.composeapp.generated.resources.settings_language
import barbcker.composeapp.generated.resources.settings_logout
import barbcker.composeapp.generated.resources.settings_logout_confirmation
import barbcker.composeapp.generated.resources.settings_privacy_policy
import barbcker.composeapp.generated.resources.settings_section_danger_zone
import barbcker.composeapp.generated.resources.settings_section_other
import barbcker.composeapp.generated.resources.settings_section_settings
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.presentation.design_system.components.BcConfirmationAlertDialog
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcSecondaryButton
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Purple900
import com.zrcoding.barbcker.presentation.design_system.theme.Red500
import com.zrcoding.barbcker.presentation.design_system.theme.Red800
import com.zrcoding.barbcker.presentation.design_system.theme.White
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.common.utils.Constants
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingRoute(
    navigateToPrivacyPolicy: () -> Unit,
    navigateToAboutUs: () -> Unit,
    navigateToAuth: () -> Unit,
    viewModel: SettingsViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    var showLanguageChooser by remember { mutableStateOf(false) }

    var showCurrencyChooser by remember { mutableStateOf(false) }
    var showLogoutConfirmation by remember { mutableStateOf(false) }
    var showDeleteAccountConfirmation by remember { mutableStateOf(false) }
    val uriHandler = LocalUriHandler.current
    Box(modifier = Modifier.fillMaxSize()) {
        SettingScreen(
            viewState = viewState,
            onShowLanguageChooser = { showLanguageChooser = true },
            onShowCurrencyChooser = { showCurrencyChooser = true },
            onShowLogoutConfirmation = { showLogoutConfirmation = true },
            onShowDeleteAccountConfirmation = { showDeleteAccountConfirmation = true },
            onContactSupport = { uriHandler.openUri(Constants.contactUsUrl) },
            openAboutUs = navigateToAboutUs,
            openPrivacyPolicy = navigateToPrivacyPolicy,
        )
        if (viewState.isLoading) {
            BasicAlertDialog(
                onDismissRequest = {},
                properties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false
                )
            ) {
                Box(
                    modifier = Modifier.size(MaterialTheme.dimension.extraExtraBig),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }
    if (showLanguageChooser) {
        SettingChooser(
            first = stringResource(Language.ENGLISH.displayName),
            second = stringResource(Language.FRENCH.displayName),
            onFirstChanged = {
                viewModel.onLanguageChanged(Language.ENGLISH)
                showLanguageChooser = false
            },
            onSecondChanged = {
                viewModel.onLanguageChanged(Language.FRENCH)
                showLanguageChooser = false
            },
            onDismissRequest = { showLanguageChooser = false }
        )
    }
    if (showCurrencyChooser) {
        SettingChooser(
            first = stringResource(Currency.DOLLAR.stringRes()),
            second = stringResource(Currency.EURO.stringRes()),
            onFirstChanged = {
                viewModel.onCurrencyChanged(Currency.DOLLAR)
                showCurrencyChooser = false
            },
            onSecondChanged = {
                viewModel.onCurrencyChanged(Currency.EURO)
                showCurrencyChooser = false
            },
            onDismissRequest = { showCurrencyChooser = false }
        )
    }
    if (showLogoutConfirmation) {
        BcConfirmationAlertDialog(
            title = stringResource(Res.string.settings_logout),
            description = stringResource(Res.string.settings_logout_confirmation),
            onConfirm = {
                showLogoutConfirmation = false
                viewModel.onLogout()
            },
            onCancel = { showLogoutConfirmation = false }
        )
    }
    if (showDeleteAccountConfirmation) {
        BcConfirmationAlertDialog(
            title = stringResource(Res.string.settings_delete_account),
            description = stringResource(Res.string.settings_delete_account_confirmation),
            onConfirm = {
                showDeleteAccountConfirmation = false
                viewModel.onDeleteAccount()
            },
            onCancel = { showDeleteAccountConfirmation = false }
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                SettingsOneTimeEvents.NavigateToAuth -> navigateToAuth()
            }
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.SETTINGS)
}

@Composable
private fun SettingScreen(
    viewState: SettingsViewState,
    onShowLanguageChooser: () -> Unit,
    onShowCurrencyChooser: () -> Unit,
    onShowLogoutConfirmation: () -> Unit,
    onShowDeleteAccountConfirmation: () -> Unit,
    onContactSupport: () -> Unit,
    openAboutUs: () -> Unit,
    openPrivacyPolicy: () -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
    ) {
        AccountInfo(viewState.shopName, viewState.email)
        SettingSection(
            title = Res.string.settings_section_settings,
            items = persistentListOf(
                SettingItem(
                    iconRes = Res.drawable.ic_language,
                    nameRes = Res.string.settings_language,
                    descriptionRes = stringResource(viewState.language.displayName),
                    onClick = onShowLanguageChooser
                ),
                SettingItem(
                    iconRes = Res.drawable.ic_currency,
                    nameRes = Res.string.settings_currency,
                    descriptionRes = stringResource(viewState.currency.stringRes()),
                    onClick = onShowCurrencyChooser
                )
            )
        )
        SettingSection(
            title = Res.string.settings_section_other,
            items = persistentListOf(
                SettingItem(
                    iconRes = Res.drawable.ic_whatsapp,
                    nameRes = Res.string.settings_contact_us,
                    descriptionRes = null,
                    onClick = onContactSupport
                ),
                SettingItem(
                    iconRes = Res.drawable.ic_info,
                    nameRes = Res.string.settings_about_us,
                    descriptionRes = null,
                    onClick = openAboutUs
                ),
                SettingItem(
                    iconRes = Res.drawable.ic_lock,
                    nameRes = Res.string.settings_privacy_policy,
                    descriptionRes = null,
                    onClick = openPrivacyPolicy
                )
            )
        )
        SettingSection(
            title = Res.string.settings_section_danger_zone,
            items = persistentListOf(
                SettingItem(
                    iconRes = Res.drawable.ic_logout,
                    nameRes = Res.string.settings_logout,
                    descriptionRes = null,
                    onClick = onShowLogoutConfirmation
                ),

                SettingItem(
                    iconRes = Res.drawable.ic_logout,
                    nameRes = Res.string.settings_delete_account,
                    descriptionRes = null,
                    onClick = onShowDeleteAccountConfirmation
                )
            ),
            isDangerous = true
        )
    }
}

@Composable
private fun AccountInfo(
    shopName: String,
    email: String,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                brush = Brush.linearGradient(
                    0.0f to Purple900,
                    1f to Red800,
                ), MaterialTheme.shapes.large
            )
            .padding(MaterialTheme.dimension.large),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
    ) {
        Image(
            modifier = Modifier.size(50.dp).clip(CircleShape),
            painter = painterResource(Res.drawable.img_barber1),
            contentDescription = "Account avatar"
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
        ) {
            Column {
                Text(
                    text = shopName,
                    style = MaterialTheme.typography.bodyLarge,
                    color = White
                )
                Text(
                    text = email,
                    style = MaterialTheme.typography.labelMedium,
                    color = White
                )
            }
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Plan: Free trial",
                        style = MaterialTheme.typography.bodyLarge,
                        color = White
                    )
                    Text(
                        text = "10 days left",
                        style = MaterialTheme.typography.labelMedium,
                        color = White
                    )
                }
                BcPrimaryButton(
                    modifier = Modifier.height(MaterialTheme.dimension.extraBig),
                    text = "Upgrade",
                    onClick = {}
                )
            }
        }
    }
}

@Composable
private fun SettingSection(
    title: StringResource,
    items: PersistentList<SettingItem>,
    isDangerous: Boolean = false,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
    ) {
        Text(
            stringResource(title),
            style = MaterialTheme.typography.bodyLarge
        )
        Card(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier.padding(MaterialTheme.dimension.large),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                items.forEachIndexed { index, it ->
                    SettingItemContent(
                        icon = it.iconRes,
                        name = it.nameRes,
                        description = it.descriptionRes,
                        isDangerous = isDangerous,
                        onClick = it.onClick
                    )
                    if (index != items.lastIndex) {
                        HorizontalDivider(color = MaterialTheme.colorScheme.outlineVariant)
                    }
                }
            }
        }
    }
}

@Composable
private fun SettingItemContent(
    icon: DrawableResource,
    name: StringResource,
    description: String?,
    isDangerous: Boolean = false,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .clickable(onClick = onClick)
            .fillMaxWidth()
            .padding(MaterialTheme.dimension.small),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(MaterialTheme.dimension.extraBig)
                    .background(
                        MaterialTheme.colorScheme.surfaceContainer,
                        MaterialTheme.shapes.small
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimension.bigger),
                    painter = painterResource(icon),
                    contentDescription = null,
                    tint = if (isDangerous) Red500 else MaterialTheme.colorScheme.onBackground
                )
            }
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(
                    text = stringResource(name),
                    style = MaterialTheme.typography.bodyLarge,
                    color = if (isDangerous) Red500 else Color.Unspecified
                )
                description?.let {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
            }
        }
        Icon(
            imageVector = Icons.AutoMirrored.Default.KeyboardArrowRight,
            contentDescription = null,
        )
    }
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun SettingChooser(
    first: String,
    second: String,
    onFirstChanged: () -> Unit,
    onSecondChanged: () -> Unit,
    onDismissRequest: () -> Unit,
) {
    BasicAlertDialog(
        modifier = Modifier
            .background(
                MaterialTheme.colorScheme.background,
                MaterialTheme.shapes.large
            )
            .padding(horizontal = MaterialTheme.dimension.medium)
            .padding(vertical = MaterialTheme.dimension.medium),
        onDismissRequest = onDismissRequest,
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier.fillMaxWidth().padding(MaterialTheme.dimension.large),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large),
            ) {
                Text(
                    text = stringResource(Res.string.settings_choose_language),
                    style = MaterialTheme.typography.titleMedium
                )
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    BcSecondaryButton(
                        modifier = Modifier.width(200.dp),
                        text = first,
                        onClick = onFirstChanged
                    )
                    BcSecondaryButton(
                        modifier = Modifier.width(200.dp),
                        text = second,
                        onClick = onSecondChanged
                    )
                }
            }
            IconButton(
                modifier = Modifier.align(Alignment.TopEnd),
                onClick = onDismissRequest
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null,
                )
            }
        }
    }
}

@Preview
@Composable
private fun SettingScreenPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            SettingScreen(
                viewState = SettingsViewState(),
                onShowLanguageChooser = {},
                onShowCurrencyChooser = {},
                onShowLogoutConfirmation = {},
                onShowDeleteAccountConfirmation = {},
                onContactSupport = {},
                openAboutUs = {},
                openPrivacyPolicy = {},
            )
        }
    }
}