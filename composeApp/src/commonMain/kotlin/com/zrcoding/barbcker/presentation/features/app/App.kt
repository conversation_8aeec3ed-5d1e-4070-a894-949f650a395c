package com.zrcoding.barbcker.presentation.features.app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tweener.passage.Passage
import com.tweener.passage.model.AppleGatekeeperConfiguration
import com.tweener.passage.model.EmailPasswordGatekeeperConfiguration
import com.tweener.passage.model.GoogleGatekeeperAndroidConfiguration
import com.tweener.passage.model.GoogleGatekeeperConfiguration
import com.zrcoding.barbcker.analytics.AnalyticsHelper
import com.zrcoding.barbcker.analytics.LocalAnalyticsHelper
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarHostForSnackBarController
import com.zrcoding.barbcker.presentation.navigation.BarbckerNavHost
import dev.gitlive.firebase.Firebase
import io.github.aakira.napier.DebugAntilog
import io.github.aakira.napier.Napier
import org.koin.compose.koinInject
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun App(appViewModel: AppViewModel = koinViewModel()) {
    val analyticsHelper = koinInject<AnalyticsHelper>()
    val passage = koinInject<Passage>()
    LaunchedEffect(passage) {

    }
    passage.bindToView()
    val startDestination = appViewModel.startDestination.collectAsStateWithLifecycle().value
    BarbckerTheme {
        CompositionLocalProvider(LocalAnalyticsHelper provides analyticsHelper) {
            Scaffold(
                snackbarHost = { SnackBarHostForSnackBarController() }
            ) { paddingValues ->
                when (startDestination) {
                    StartDestination.Loading -> CircularProgressIndicator()
                    is StartDestination.Ready -> BarbckerNavHost(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(paddingValues)
                            .background(MaterialTheme.colorScheme.background),
                        startDestination = startDestination,
                    )
                }
            }
        }
    }
}