package com.zrcoding.barbcker.presentation.features.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.revenuecat.purchases.kmp.LogLevel
import com.revenuecat.purchases.kmp.Purchases
import com.revenuecat.purchases.kmp.configure
import com.revenuecat.purchases.kmp.ktx.awaitLogIn
import com.tweener.passage.Passage
import com.tweener.passage.model.AppleGatekeeperConfiguration
import com.tweener.passage.model.EmailPasswordGatekeeperConfiguration
import com.tweener.passage.model.GoogleGatekeeperAndroidConfiguration
import com.tweener.passage.model.GoogleGatekeeperConfiguration
import com.zrcoding.barbcker.config.Config
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import dev.gitlive.firebase.Firebase
import io.github.aakira.napier.DebugAntilog
import io.github.aakira.napier.Napier
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module

class AppViewModel(
    private val accountRepository: AccountRepository,
    private val barberRepository: BarberRepository,
    val passage: Passage,
) : ViewModel() {

    private val _startDestination = MutableStateFlow<StartDestination>(StartDestination.Loading)
    val startDestination = _startDestination.asStateFlow()

    init {
        initializeSdks()
        getStartDestination()
        observeAccount()
    }

    fun getStartDestination() {
        viewModelScope.launch {
            val isLanguageSelected = accountRepository.isLanguageSelected()
            _startDestination.value = if (!isLanguageSelected) {
                StartDestination.Ready.Onboarding
            } else {
                when (val account = accountRepository.getAccount().firstOrNull()) {
                    is Account.Connected -> {
                        if (account.shopName.isEmpty()) {
                            StartDestination.Ready.CompleteAccount
                        } else {
                            val barbers = barberRepository.getCount()
                            if (barbers == 0) {
                                StartDestination.Ready.SetupBarbers
                            } else StartDestination.Ready.Home
                        }
                    }

                    else -> StartDestination.Ready.Auth
                }
            }
        }
    }

    private fun observeAccount() {
        viewModelScope.launch {
            accountRepository.getAccount().collectLatest { account ->
                when (account) {
                    is Account.Connected -> {
                        loadKoinModules(module { single<Account.Connected> { account } })
                        Purchases.sharedInstance.run {
                            awaitLogIn(account.uid)
                            setEmail(account.email)
                            setDisplayName(account.displayName)
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    private fun initializeSdks() {
        passage.initialize(
            gatekeeperConfigurations = listOf(
                EmailPasswordGatekeeperConfiguration,
                GoogleGatekeeperConfiguration(
                    serverClientId = "************-d80dqau2tqek5p5e1vig6mumec39hdv7.apps.googleusercontent.com",
                    android = GoogleGatekeeperAndroidConfiguration(
                        filterByAuthorizedAccounts = false,
                        autoSelectEnabled = true,
                        maxRetries = 1
                    )
                ),
                AppleGatekeeperConfiguration(),
            ),
            firebase = Firebase
        )
        Napier.base(DebugAntilog())
        Purchases.logLevel = LogLevel.DEBUG
        Purchases.configure(apiKey = Config.revenueCatApiKey) {}
    }
}