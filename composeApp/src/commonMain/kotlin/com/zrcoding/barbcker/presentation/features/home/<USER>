package com.zrcoding.barbcker.presentation.features.home

import androidx.lifecycle.ViewModel
import com.revenuecat.purchases.kmp.Purchases
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class HomeViewModel() : ViewModel() {
    private val canCreateHaircut = MutableStateFlow(false)
    val canCreateHaircutFlow = canCreateHaircut.asStateFlow()

    init {
        Purchases.sharedInstance.getCustomerInfo(
            onSuccess = { customerInfo ->
                canCreateHaircut.value =
                    customerInfo.entitlements.active["full_access"]?.isActive == true
            },
            onError = { error ->
                // Handle error
            }
        )
    }
}