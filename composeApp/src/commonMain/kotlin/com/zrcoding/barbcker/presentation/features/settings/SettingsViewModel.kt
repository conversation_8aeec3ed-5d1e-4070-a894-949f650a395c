package com.zrcoding.barbcker.presentation.features.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_network_error_verify_and_try_later
import barbcker.composeapp.generated.resources.settings_currency_change_success
import barbcker.composeapp.generated.resources.settings_logout_success
import com.revenuecat.purchases.kmp.Purchases
import com.revenuecat.purchases.kmp.PurchasesDelegate
import com.revenuecat.purchases.kmp.models.CustomerInfo
import com.revenuecat.purchases.kmp.models.PurchasesError
import com.revenuecat.purchases.kmp.models.StoreProduct
import com.revenuecat.purchases.kmp.models.StoreTransaction
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.presentation.common.extension.renderFailure
import com.zrcoding.barbcker.presentation.common.extension.renderSuccess
import com.zrcoding.barbcker.presentation.design_system.utils.UiText
import io.github.aakira.napier.Napier
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
class SettingsViewModel(
    private val accountRepository: AccountRepository,
    private val localization: Localization,
) : ViewModel() {

    private val _viewState = MutableStateFlow(
        SettingsViewState(language = Language.fromCode(localization.getCurrentLanguage()))
    )
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<SettingsOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    init {
        observeAccount()
        Purchases.sharedInstance.getCustomerInfo(
            onSuccess = { customerInfo ->
                Napier.d {
                    "Entitlement: ${customerInfo.entitlements.active.toString()}"
                }
            },
            onError = { error ->
                // Handle error
            }
        )
        Purchases.sharedInstance.delegate = object : PurchasesDelegate {
            override fun onPurchasePromoProduct(
                product: StoreProduct,
                startPurchase: ((PurchasesError, Boolean) -> Unit, (StoreTransaction, CustomerInfo) -> Unit) -> Unit
            ) {}

            override fun onCustomerInfoUpdated(customerInfo: CustomerInfo) {
                Napier.d {
                    "Entitlement: ${customerInfo.entitlements.active.toString()}"
                }
            }
        }
    }

    fun onCurrencyChanged(currency: Currency) {
        if (viewState.value.currency == currency) return
        viewModelScope.launch {
            val result = accountRepository.updateAccount(viewState.value.shopName, currency)
            when (result) {
                is Resource.Success -> renderSuccess(UiText.FromRes(Res.string.settings_currency_change_success))
                is Resource.Failure -> renderFailure(UiText.FromRes(Res.string.common_network_error_verify_and_try_later))
            }
        }
    }

    fun onLanguageChanged(language: Language) {
        if (viewState.value.language == language) return
        localization.changeLanguage(language.code)
        _viewState.update { it.copy(language = language) }
    }

    fun onLogout() {
        viewModelScope.launch {
            _viewState.update { it.copy(isLoading = true) }
            val result = accountRepository.logout()
            _viewState.update { it.copy(isLoading = false) }
            when (result) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(Res.string.settings_logout_success))
                    _oneTimeEvents.emit(SettingsOneTimeEvents.NavigateToAuth)
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    fun onDeleteAccount() {
        viewModelScope.launch {
            _viewState.update { it.copy(isLoading = true) }
            val result = accountRepository.deleteAccount()
            _viewState.update { it.copy(isLoading = false) }
            when (result) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(Res.string.settings_logout_success))
                    _oneTimeEvents.emit(SettingsOneTimeEvents.NavigateToAuth)
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    private fun observeAccount() {
        viewModelScope.launch {
            accountRepository.getAccount().collectLatest { account ->
                (account as? Account.Connected)?.let { connected ->
                    _viewState.update {
                        it.copy(
                            shopName = connected.shopName,
                            email = connected.email.orEmpty(),
                            currency = connected.currency
                        )
                    }
                }
            }
        }
    }
}