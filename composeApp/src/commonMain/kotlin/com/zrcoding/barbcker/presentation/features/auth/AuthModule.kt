package com.zrcoding.barbcker.presentation.features.auth

import com.zrcoding.barbcker.presentation.features.auth.password_reset.ResetPasswordViewModel
import com.zrcoding.barbcker.presentation.features.auth.sign_in.SignInViewModel
import com.zrcoding.barbcker.presentation.features.auth.sign_up.SignUpViewModel
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

val authModule = module {
    viewModelOf(::SignInViewModel)
    viewModelOf(::SignUpViewModel)
    viewModelOf(::ResetPasswordViewModel)
}