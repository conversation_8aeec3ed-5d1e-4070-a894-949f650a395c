package com.zrcoding.barbcker.presentation.navigation

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import com.revenuecat.purchases.kmp.ui.revenuecatui.Paywall
import com.revenuecat.purchases.kmp.ui.revenuecatui.PaywallOptions
import com.zrcoding.barbcker.presentation.features.app.StartDestination
import com.zrcoding.barbcker.presentation.features.auth.password_reset.ResetPasswordRoute
import com.zrcoding.barbcker.presentation.features.auth.sign_in.SignInRoute
import com.zrcoding.barbcker.presentation.features.auth.sign_up.SignUpRoute
import com.zrcoding.barbcker.presentation.features.barber.setup.SetupBarbersRoute
import com.zrcoding.barbcker.presentation.features.barber.upsert.UpsertBarberBottomSheet
import com.zrcoding.barbcker.presentation.features.common.utils.Constants
import com.zrcoding.barbcker.presentation.features.complete_account.CompleteAccountRoute
import com.zrcoding.barbcker.presentation.features.haircut.ceate.CreateHaircutRoute
import com.zrcoding.barbcker.presentation.features.haircut.list.HaircutListRoute
import com.zrcoding.barbcker.presentation.features.home.HomeScreen
import com.zrcoding.barbcker.presentation.features.home.rememberAppState
import com.zrcoding.barbcker.presentation.features.onboarding.OnboardingRoute
import com.zrcoding.barbcker.presentation.features.webview.WebViewScreen

@Composable
fun BarbckerNavHost(
    modifier: Modifier = Modifier,
    startDestination: StartDestination.Ready,
) {
    val navController = rememberNavController()
    NavHost(
        navController = navController,
        startDestination = when (startDestination) {
            StartDestination.Ready.Onboarding -> Onboarding
            StartDestination.Ready.Auth -> SignIn
            StartDestination.Ready.CompleteAccount -> CompleteAccount
            StartDestination.Ready.SetupBarbers -> SetupBarbers
            StartDestination.Ready.Home -> Home
        },
        modifier = modifier
    ) {
        composableWithAnimation<Onboarding> {
            OnboardingRoute(
                navigateToAuth = {
                    navController.navigate(SignIn) {
                        popUpTo(Onboarding) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        composableWithAnimation<SignIn> {
            SignInRoute(
                navigateToSignUp = {
                    navController.navigate(SignUp)
                },
                navigateToPasswordReset = {
                    navController.navigate(PasswordReset)
                },
                navigateToCompleteAccount = {
                    navController.navigate(CompleteAccount)
                },
                navigateToSetupBarbers = {
                    navController.navigate(SetupBarbers)
                },
                navigateToHome = {
                    navController.navigate(Home) {
                        popUpTo(SignIn) {
                            inclusive = true
                        }
                    }
                },
                navigateToPrivacyPolicy = {
                    navController.navigate(WebViewScreen(url = Constants.privacyPolicyUrl))
                }
            )
        }
        composableWithAnimation<SignUp> {
            SignUpRoute(
                navigateBack = {
                    navController.popBackStack()
                },
                navigateToCompleteAccount = {
                    navController.navigate(CompleteAccount)
                },
                navigateToPrivacyPolicy = {
                    navController.navigate(WebViewScreen(url = Constants.privacyPolicyUrl))
                }
            )
        }
        composableWithAnimation<PasswordReset> {
            ResetPasswordRoute(
                navigateBack = {
                    navController.popBackStack()
                },
                navigateToPrivacyPolicy = {
                    navController.navigate(WebViewScreen(url = Constants.privacyPolicyUrl))
                }
            )
        }
        composableWithAnimation<CompleteAccount> {
            CompleteAccountRoute(
                navigateBack = { navController.popBackStack() },
                navigateToSetupBarbers = {
                    navController.navigate(SetupBarbers)
                },
                navigateToAuth = {
                    navController.popBackStack()
                }
            )
        }
        composableWithAnimation<SetupBarbers> {
            SetupBarbersRoute(
                navigateBack = { navController.popBackStack() },
                navigateToUpsertBarber = {
                    navController.navigate(UpsertBarber(barberId = it?.uuid))
                },
                navigateToHome = {
                    navController.navigate(Home) {
                        popUpTo(SetupBarbers) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        dialog<UpsertBarber> {
            UpsertBarberBottomSheet(
                onSuccess = {
                    navController.popBackStack()
                }
            )
        }
        composableWithAnimation<Home> {
            val appState = rememberAppState(navController = navController)
            HomeScreen(
                appState = appState,
                navigateToUpsertBarber = {
                    navController.navigate(UpsertBarber(barberId = it))
                },
                navigateToHaircutList = {
                    navController.navigate(HaircutList(barberId = it))
                },
                navigateToPrivacyPolicy = {
                    navController.navigate(WebViewScreen(url = Constants.privacyPolicyUrl))
                },
                navigateToAboutUs = {
                    navController.navigate(WebViewScreen(url = Constants.aboutUsUrl))
                },
                navigateToAuth = {
                    navController.navigate(SignIn) {
                        popUpTo(Home) {
                            inclusive = true
                        }
                    }
                },
                navigateToPaywall = {
                    navController.navigate(PaywallScreen)
                }
            )
        }
        dialog<CreateHaircut>(
            dialogProperties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false,
            )
        ) {
            CreateHaircutRoute(
                navigateBack = { navController.popBackStack() },
            )
        }
        composableWithAnimation<HaircutList> {
            HaircutListRoute(
                navigateBack = { navController.popBackStack() },
            )
        }
        composableWithAnimation<WebViewScreen> {
            val route = it.toRoute<WebViewScreen>()
            WebViewScreen(
                title = route.title,
                url = route.url,
                navigateBack = { navController.navigateUp() }
            )
        }
        composableWithAnimation<PaywallScreen> {
            val paywallOptions = remember {
                PaywallOptions(dismissRequest = { navController.popBackStack() })
            }
            Paywall(options = paywallOptions)
        }
    }
}

private inline fun <reified T : Any> NavGraphBuilder.composableWithAnimation(
    duration: Int = 400,
    noinline composable: @Composable AnimatedContentScope.(NavBackStackEntry) -> Unit
) {
    composable<T>(
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        }
    ) {
        composable(it)
    }
}