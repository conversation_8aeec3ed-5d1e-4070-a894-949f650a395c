[versions]
kotlin = "2.2.0"
agp = "8.11.1"
android-targetSdk = "36"
android-compileSdk = "36"
android-minSdk = "24"
compose_multiplatform = "1.8.2"
google_services = "4.4.3"
ksp = "2.2.0-2.0.2"
wire = "5.3.5"
crashlytics = "3.0.6"

#android
androidx-activity = "1.10.1"
androidx-core-splashscreen = "1.0.1"
androidx-core = "1.16.0"
material = "1.12.0"
integrity = "1.5.0"

#multiplatform
androidx-navigation = "2.9.0-beta03"
androidx-lifecycle = "2.9.1"
androidx-dataStore = "1.1.7"
room = "2.7.2"
sqlite = "2.5.2"
kotlinx-serialization = "1.9.0"
kotlinx-datetime = "0.7.1-0.6.x-compat"
kotlinx-coroutines = "1.10.2"
kotlinx-collections = "0.4.0"
koin-bom = "4.1.0"
firebase-bom = "33.16.0"
gitlive = "2.1.0"
passage = "1.4.1"
androidx-paging = "3.3.6"
cashapp-paging = "3.2.0-alpha05-0.2.3"
napier = "2.7.1"
webview = "2.0.2"
purchases-kmp = "2.1.2+17.5.0"

#test
junit = "4.13.2"
turbine = "1.1.0"
androidx-paging-testing = "3.3.6"

[libraries]
#android
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "androidx-activity" }
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "androidx-core-splashscreen" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidx-core" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
integrity = { module = "com.google.android.play:integrity", version.ref = "integrity" }

#multiplatform
androidx-navigation = { module = "org.jetbrains.androidx.navigation:navigation-compose", version.ref = "androidx-navigation" }
androidx-lifecycle-viewmodel = { module = "org.jetbrains.androidx.lifecycle:lifecycle-viewmodel", version.ref = "androidx-lifecycle" }
androidx-lifecycle-runtimeCompose = { module = "org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose", version.ref = "androidx-lifecycle" }
androidx-datastore = { group = "androidx.datastore", name = "datastore", version.ref = "androidx-dataStore" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "androidx-dataStore" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-paging = { group = "androidx.room", name = "room-paging", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-sqlite-bundled = { group = "androidx.sqlite", name = "sqlite-bundled", version.ref = "sqlite" }

androidx-paging = { module = "androidx.paging:paging-runtime", version.ref = "androidx-paging" }
cashapp-paging-common = { group = "app.cash.paging", name = "paging-common", version.ref = "cashapp-paging" }
cashapp-paging-compose = { group = "app.cash.paging", name = "paging-compose-common", version.ref = "cashapp-paging" }

kotlinx-serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinx-serialization" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinx-datetime" }
kotlinx-coroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinx-coroutines" }
kotlinx-collections = { module = "org.jetbrains.kotlinx:kotlinx-collections-immutable", version.ref = "kotlinx-collections" }

koin-bom = { module = "io.insert-koin:koin-bom", version.ref = "koin-bom" }
koin-core = { module = "io.insert-koin:koin-core" }
koin-android = { module = "io.insert-koin:koin-android" }
koin-compose = { module = "io.insert-koin:koin-compose" }
koin-compose-viewmodel = { module = "io.insert-koin:koin-compose-viewmodel" }

firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
gitlive-analytics = { group = "dev.gitlive", name = "firebase-analytics", version.ref = "gitlive" }
gitlive-crashlytics = { group = "dev.gitlive", name = "firebase-crashlytics", version.ref = "gitlive" }
gitlive-auth = { group = "dev.gitlive", name = "firebase-auth", version.ref = "gitlive" }
gitlive-firestore = { group = "dev.gitlive", name = "firebase-firestore", version.ref = "gitlive" }
passage = { group = "io.github.tweener", name = "passage", version.ref = "passage" }
napier = { group = "io.github.aakira", name = "napier", version.ref = "napier" }
webview = { group = "io.github.kevinnzou", name = "compose-webview-multiplatform", version.ref = "webview" }

purchases-core = { module = "com.revenuecat.purchases:purchases-kmp-core", version.ref = "purchases-kmp" }
purchases-ui = { module = "com.revenuecat.purchases:purchases-kmp-ui", version.ref = "purchases-kmp" }
purchases-datetime = { module = "com.revenuecat.purchases:purchases-kmp-datetime", version.ref = "purchases-kmp" }
purchases-either = { module = "com.revenuecat.purchases:purchases-kmp-either", version.ref = "purchases-kmp" }
purchases-result = { module = "com.revenuecat.purchases:purchases-kmp-result", version.ref = "purchases-kmp" }

#test
junit = { module = "junit:junit", version.ref = "junit" }
kotlin-test = { module = "org.jetbrains.kotlin:kotlin-test", version.ref = "kotlin" }
kotlin-testJunit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlin" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinx-coroutines" }
koin-test = { module = "io.insert-koin:koin-test", version.ref = "koin-bom" }
turbine = { module = "app.cash.turbine:turbine", version.ref = "turbine" }
androidx-room-testing = { module = "androidx.room:room-testing", version.ref = "room" }
androidx-paging-testing = { module = "androidx.paging:paging-testing", version.ref = "androidx-paging-testing" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
composeMultiplatform = { id = "org.jetbrains.compose", version.ref = "compose_multiplatform" }
composeCompiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlinMultiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
kotlinxSerialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
googleServices = { id = "com.google.gms.google-services", version.ref = "google_services" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
squareuWire = { id = "com.squareup.wire", version.ref = "wire" }
room = { id = "androidx.room", version.ref = "room" }
crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }
